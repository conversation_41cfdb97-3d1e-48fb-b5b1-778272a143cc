using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of IWalletService for testing
/// </summary>
public class MockWalletService : IWalletService
{
    private readonly Dictionary<(int UserId, int CoinId), Wallet> _wallets = new();
    private int _nextId = 1;
    private bool _shouldFail = false;

    // Track method calls for verification
    public int AddToWalletAsyncCallCount { get; private set; }
    public List<(int UserId, int CoinId, decimal Amount)> AddToWalletAsyncCalls { get; } = new();

    public void SetAddToWalletAsyncToFail()
    {
        _shouldFail = true;
    }

    public Task<Wallet?> GetByIdAsync(int id)
    {
        var wallet = _wallets.Values.FirstOrDefault(w => w.Id == id);
        return Task.FromResult(wallet);
    }

    public Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        var wallets = _wallets.Values.Where(w => w.UserId == userId).ToList();
        return Task.FromResult(wallets);
    }

    public Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        var wallets = _wallets.Values
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => w.Balance)
            .Take(topN)
            .ToList();
        return Task.FromResult(wallets);
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId)
    {
        _wallets.TryGetValue((userId, coinId), out var wallet);
        return Task.FromResult(wallet);
    }

    public Task<List<WalletViewModel>> GetListAsync()
    {
        var viewModels = _wallets.Values.Select(w => new WalletViewModel
        {
            Id = w.Id,
            UserId = w.UserId,
            CoinId = w.CoinId,
            Balance = w.Balance,
            CreatedDate = w.CreatedDate,
            ModifiedDate = w.ModifiedDate
        }).ToList();
        return Task.FromResult(viewModels);
    }

    //public async Task<decimal> GetUserBalanceAsync(int userId, int coinId)
    //{
    //    var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
    //    return wallet?.Balance ?? 0;
    //}

    public Task UpdateAsync(Wallet wallet)
    {
        if (_wallets.ContainsKey((wallet.UserId, wallet.CoinId)))
        {
            _wallets[(wallet.UserId, wallet.CoinId)] = wallet;
        }
        return Task.CompletedTask;
    }

    public Task DeleteAsync(int id)
    {
        var wallet = _wallets.Values.FirstOrDefault(w => w.Id == id);
        if (wallet != null)
        {
            _wallets.Remove((wallet.UserId, wallet.CoinId));
        }
        return Task.CompletedTask;
    }

    //public async Task<Wallet> AddBalanceAsync(int userId, int coinId, decimal amount)
    //{
    //    if (_shouldFail)
    //    {
    //        throw new Exception("Simulated wallet service failure");
    //    }

    //    AddToWalletAsyncCallCount++;
    //    AddToWalletAsyncCalls.Add((userId, coinId, amount));
    //    var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);

    //    if (wallet == null)
    //    {
    //        wallet = new Wallet
    //        {
    //            UserId = userId,
    //            CoinId = coinId,
    //            Balance = amount,
    //            CreatedDate = DateTime.UtcNow
    //        };
    //        return await CreateAsync(wallet);
    //    }
    //    else
    //    {
    //        wallet.Balance += amount;
    //        wallet.ModifiedDate = DateTime.UtcNow;
    //        await UpdateAsync(wallet);
    //        return wallet;
    //    }
    //}

    public async Task<bool> DeductBalanceAsync(int userId, int coinId, decimal amount)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);

        if (wallet == null || wallet.Balance < amount)
        {
            return false;
        }

        wallet.Balance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet);
        return true;
    }

    //public async Task<Wallet> AddToWalletAsync(int userId, int coinId, decimal amount)
    //{
    //    if (_shouldFail)
    //    {
    //        throw new Exception("Simulated wallet service failure");
    //    }

    //    AddToWalletAsyncCallCount++;
    //    AddToWalletAsyncCalls.Add((userId, coinId, amount));

    //    return await AddBalanceAsync(userId, coinId, amount);
    //}

    // Helper method to verify calls
    public bool VerifyAddToWalletAsync(int userId, int coinId, decimal amount, int times = 1, decimal tolerance = 0.01m)
    {
        return AddToWalletAsyncCalls.Count(call =>
            call.UserId == userId &&
            call.CoinId == coinId &&
            Math.Abs(call.Amount - amount) <= tolerance) == times;
    }

    // Helper method to debug wallet calls
    public List<(int UserId, int CoinId, decimal Amount)> GetAddToWalletAsyncCalls()
    {
        return AddToWalletAsyncCalls;
    }

    // NEW METHODS - Available Balance (Phase 2)
    public async Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        // Available balance = Balance
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.Balance ?? 0;
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId, AppDbContext? existingContext = null)
    {
      return  GetByUserIdAndCoinIdAsync(userId, coinId);
    }

    public Task<Wallet> CreateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        wallet.Id = _nextId++;
        _wallets[(wallet.UserId, wallet.CoinId)] = wallet;
        return Task.FromResult(wallet);
    }

    public Task UpdateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        return UpdateAsync(wallet);
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext? existingContext = null)
    {
        if (_shouldFail)
        {
            throw new Exception("Simulated wallet service failure");
        }

        AddToWalletAsyncCallCount++;
        AddToWalletAsyncCalls.Add((userId, rzwTokenInfo.TokenId, amount));
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null)
        {
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                CreatedDate = DateTime.UtcNow
            };
            return await CreateAsync(wallet);
        }
        else
        {
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet);
            return wallet;
        }
    }
}
