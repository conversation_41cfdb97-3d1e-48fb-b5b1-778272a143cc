using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of ITokenPriceService for testing
/// </summary>
public class MockTokenPriceService : ITokenPriceService
{
    private readonly decimal _buyPrice;
    private readonly decimal _sellPrice;
    private readonly int _coinId;

    public MockTokenPriceService(decimal buyPrice = 1.0m, int rzwTokenId = 1, decimal? sellPrice = null)
    {
        _buyPrice = buyPrice;
        _coinId = rzwTokenId;
        _sellPrice = sellPrice ?? buyPrice * 0.95m; // Default sell price is 5% higher than buy price
    }
    public Task<RzwTokenInfo> GetCoinInfoAsync(int coinId)
    {
        return Task.FromResult(new RzwTokenInfo { TokenId = coinId, BuyPrice = _buyPrice, SellPrice = _sellPrice });
    }

    /// <summary>
    /// Gets the current RZW token buy price in TRY
    /// </summary>
    public Task<decimal> GetCurrentRzwBuyPriceAsync()
    {
        return Task.FromResult(_buyPrice);
    }

    /// <summary>
    /// Gets the RZW token ID
    /// </summary>
    public Task<int> GetRzwTokenIdAsync()
    {
        return Task.FromResult(_coinId);
    }

    /// <summary>
    /// Gets complete RZW token information including ID and prices
    /// </summary>
    public Task<RzwTokenInfo> GetRzwTokenInfoAsync()
    {
        return Task.FromResult(new RzwTokenInfo
        {
            TokenId = _coinId,
            BuyPrice = _buyPrice,
            SellPrice = _sellPrice
        });
    }
}
